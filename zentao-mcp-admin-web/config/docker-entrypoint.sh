#!/bin/sh
# ============================================================================
# Nginx Docker 入口脚本 - 支持环境变量替换
# ============================================================================

set -e

# 默认环境变量
BACKEND_HOST=${BACKEND_HOST:-"zentao-backend-prod"}
BACKEND_PORT=${BACKEND_PORT:-"8000"}

echo "配置Nginx环境变量:"
echo "  BACKEND_HOST: $BACKEND_HOST"
echo "  BACKEND_PORT: $BACKEND_PORT"

# 替换nginx配置中的环境变量
envsubst '${BACKEND_HOST} ${BACKEND_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

echo "Nginx配置已更新:"
grep -A 3 -B 1 "proxy_pass" /etc/nginx/nginx.conf || echo "未找到proxy_pass配置"

# 验证nginx配置
echo "验证Nginx配置..."
nginx -t

# 启动nginx
echo "启动Nginx..."
exec nginx -g "daemon off;"
