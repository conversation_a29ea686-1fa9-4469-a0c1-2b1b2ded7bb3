# 测试环境Dockerfile
# 使用ARG支持包管理器镜像源配置
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

FROM python:3.12-slim

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

# 设置工作目录
WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

# 配置pip镜像源和APT镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装系统依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源
    echo "deb https://${APT_MIRROR}/debian/ bookworm main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ bookworm-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ bookworm-security main" >> /etc/apt/sources.list; \
    # 更新包列表并安装系统依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        locales; \
    # 配置语言环境
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖（使用国内镜像源）
RUN set -eux; \
    if [ -n "${PIP_INDEX_URL}" ] && [ -n "${PIP_TRUSTED_HOST}" ]; then \
        pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} -r requirements.txt; \
    else \
        pip install --no-cache-dir -r requirements.txt; \
    fi; \
    # 清理pip缓存
    pip cache purge || true;

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p data logs

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app

# 切换到非root用户
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
