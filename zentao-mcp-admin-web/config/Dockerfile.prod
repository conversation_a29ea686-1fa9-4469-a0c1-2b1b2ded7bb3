# 生产环境Dockerfile - 多阶段构建优化
# 使用ARG支持包管理器镜像源配置
ARG NPM_REGISTRY=https://registry.npmmirror.com
ARG APK_MIRROR=mirrors.aliyun.com

FROM node:18-alpine AS builder

# 重新声明ARG变量（FROM之后需要重新声明）
ARG NPM_REGISTRY
ARG APK_MIRROR

WORKDIR /app

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 安装系统依赖
RUN apk add --no-cache curl tzdata

# 配置npm镜像源
RUN npm config set registry ${NPM_REGISTRY}

# 复制package文件
COPY package*.json ./
COPY bun.lock* ./

# 安装依赖（ARM64架构跳过bun，修复rollup问题）
RUN if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        npm install -g bun && bun install; \
    else \
        npm ci; \
    fi

# 复制源代码
COPY . .

# 构建应用（生产优化）
ENV NODE_ENV=production
RUN if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 重新声明ARG变量（FROM之后需要重新声明）
ARG APK_MIRROR

# 配置Alpine镜像源并安装必要工具
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories && \
    apk add --no-cache curl gettext

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置模板和入口脚本
COPY config/nginx.conf.template /etc/nginx/nginx.conf.template
COPY config/docker-entrypoint.sh /docker-entrypoint.sh

# 设置入口脚本权限
RUN chmod +x /docker-entrypoint.sh

# 创建SSL目录并复制SSL证书（如果存在）
RUN mkdir -p /etc/nginx/ssl
RUN if [ -d "config/ssl" ] && [ "$(ls -A config/ssl 2>/dev/null)" ]; then \
        cp config/ssl/* /etc/nginx/ssl/; \
    fi

# 创建非root用户（如果不存在）
RUN if ! getent group nginx >/dev/null 2>&1; then addgroup -g 1001 -S nginx; fi && \
    if ! getent passwd nginx >/dev/null 2>&1; then adduser -S nginx -u 1001; fi

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# 设置SSL目录权限（如果存在）
RUN if [ -d "/etc/nginx/ssl" ]; then \
        chown -R nginx:nginx /etc/nginx/ssl; \
    fi

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080 8443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["/docker-entrypoint.sh"]
